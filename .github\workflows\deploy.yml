name: Deploy to GitHub Page
on:
  push:
    branches: [main]
jobs:
  build:
    name: build and deploy
    env:
      COMMIT_SECRET: ${{secrets.COMMIT_SECRET}}
      USER_NAME: songlh
      USER_EMAIL: <EMAIL>
      DOCKER_DIR: /app/dist
      GITHUB_REPOSITORY: paint-board
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v2

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v1

      - name: Build Docker image
        run: |
          docker build --target build-stage -t paint-board .

      - name: Copy build output
        run: |
          container_id=$(docker create paint-board)
          docker cp $container_id:${DOCKER_DIR} ./dist
          docker rm $container_id

      - name: Deploy to GitHub Pages
        run: |
          cd ./dist
          git init
          git config --local user.name $USER_NAME
          git config --local user.email $USER_EMAIL
          git add --all
          git commit -m "Deploy to GitHub Pages"
          git branch -M main
          git push --force https://$<EMAIL>/$GITHUB_REPOSITORY.git main:gh-pages
