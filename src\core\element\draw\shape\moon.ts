import { fabric } from 'fabric'
import { getRandomInt } from '@/utils/index'
import { generateRandomCoordinates } from '../utils'
import useDrawStore from '@/store/draw'

export function drawMoon(point: fabric.Point, size: number) {
  if (useDrawStore.getState().drawShapeCount === 1) {
    const moon = drawMoonItem(size)
    moon.set({
      left: point.x - size,
      top: point.y - size,
      fill: useDrawStore.getState().drawColors[0]
    })
    return moon
  } else {
    const points = generateRandomCoordinates(
      point?.x,
      point?.y,
      size * 3,
      useDrawStore.getState().drawShapeCount
    )
    const moons = points.map((item, index) => {
      const color =
        index > useDrawStore.getState().drawColors.length - 1
          ? useDrawStore.getState().drawColors[0]
          : useDrawStore.getState().drawColors[index]
      const moon = drawMoonItem(size)
      moon.set({
        left: item.x,
        top: item.y,
        fill: color
      })
      return moon
    })
    const group = new fabric.Group(moons)
    return group
  }
}

function drawMoonItem(size: number) {
  const maxSize = getRandomInt(size * 1.5, size * 3)
  const path = `M8.23129 2.24048C9.24338 1.78695 10.1202 2.81145 9.80357 3.70098C8.72924 6.71928 9.38932 10.1474 11.6193 12.3765C13.8606 14.617 17.3114 15.2755 20.3395 14.1819C21.2206 13.8637 22.2173 14.7319 21.7817 15.7199C21.7688 15.7491 21.7558 15.7782 21.7427 15.8074C20.9674 17.5266 19.7272 19.1434 18.1227 20.2274C16.4125 21.3828 14.3957 22.0001 12.3316 22.0001H12.3306C9.93035 21.9975 7.6057 21.1603 5.75517 19.6321C3.90463 18.1039 2.64345 15.9797 2.18793 13.6237C1.73241 11.2677 2.11094 8.82672 3.2586 6.71917C4.34658 4.72121 6.17608 3.16858 8.20153 2.25386L8.23129 2.24048Z`
  const shape = new fabric.Path(path, {
    opacity: Math.random()
  })
  const boundingRect = shape.getBoundingRect()

  const scaleX = maxSize / boundingRect.width
  const scaleY = maxSize / boundingRect.height
  const scaleToFit = Math.min(scaleX, scaleY)

  shape.scale(scaleToFit)
  return shape
}
