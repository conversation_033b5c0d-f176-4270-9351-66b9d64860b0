worker_processes  auto;
events {
  worker_connections  1024;
}

http {
  include       mime.types;
  default_type  application/octet-stream;

  sendfile on;
  keepalive_timeout 65;

  add_header 'Access-Control-Allow-Origin' '*';

  server {
    listen 80;

      location /paint-board {
        alias /usr/share/nginx/html/dist;
        try_files $uri $uri/ /index.html;
        index  index.html;
      }

      location / {
        return 301 $scheme://$http_host/paint-board/;
      }
  }
}
