@import url('https://fonts.googleapis.com/css2?family=Fredoka+One:wght@400;700&display=swap');
@import url('https://fonts.font.im/css?family=Hanalei+Fill:wght@400;700&display=swap');
@import url('https://fonts.font.im/css?family=Ruslan+Display:wght@400;700&display=swap');
@import url('https://fonts.font.im/css?family=Lobster:wght@400;700&display=swap');
@import url('https://fonts.font.im/css?family=Pacifico:wght@400;700&display=swap');
@import url('https://fonts.font.im/css?family=Gloria+Hallelujah:wght@400;700&display=swap');
@import url('https://fonts.font.im/css?family=Permanent+Marker:wght@400;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  /* Hide scrollbar for Chrome, Safari and Opera */
  .noScrollbar::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for IE, Edge and Firefox */
  .noScrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }
}

.colorInput {
  width: 1.75rem;
  height: 1.75rem;
  padding: 0;
  cursor: pointer;
  border: none;
  border-radius: 4px;
}

.colorInput::-webkit-color-swatch-wrapper {
  width: 1.75rem;
  height: 1.75rem;
  padding: 0;
  border: none;
  border-radius: 4px;
}

.colorInput::-webkit-color-swatch {
  width: 1.75rem;
  height: 1.75rem;
  padding: 0;
  border: none;
  border-radius: 4px;
}

body {
  overflow: hidden;
}

.btn:not(.btn-active):hover {
  color: #ffffff80;
  background-color: hsl(var(--n, var(--n)) / var(--tw-bg-opacity));
  border-color: hsl(var(--n, var(--n)) / var(--tw-border-opacity));
}

.tab:not(.tab-active):hover {
  color: #ffffff80;
}

.radial-progress:hover::after {
  opacity: 0.5;
}

.radial-progress:hover::before {
  opacity: 0.5;
}
