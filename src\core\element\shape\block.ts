import { fabric } from 'fabric'
import { paintBoard } from '@/core/paintBoard'
import { initCustomObjectAttr } from '@/core/utils/object'
import { getFillStyle, getShapeBorder, getShapeBorderWidth } from './utils'
import useShapeStore from '@/store/shape'
import { ELEMENT_CUSTOM_TYPE } from '@/constants'

export class BlockShap {
  shapeInstance: fabric.Object | undefined
  startX = 0
  startY = 0

  constructor(point: fabric.Point | undefined) {
    if (!point) {
      return
    }

    const strokeWidth = getShapeBorderWidth()

    const pathStr = `m426.66667,0c235.648,0 426.66667,191.06134 426.66667,426.66667c0,235.69067 -191.01867,426.66667 -426.66667,426.66667c-235.60533,0 -426.66667,-190.976 -426.66667,-426.66667c0,-235.56267 191.06134,-426.66667 426.66667,-426.66667zm0,64a362.66667,362.66667 0 1 0 0,725.33334a362.66667,362.66667 0 0 0 0,-725.33334zm-181.33333,330.66667l362.66666,0a32,32 0 0 1 4.352,63.70133l-4.352,0.29867l-362.66666,0a32,32 0 0 1 -4.352,-63.70133l4.352,-0.29867l362.66666,0l-362.66666,0z`

    const path = new fabric.Path(pathStr, {
      left: point.x,
      top: point.y,
      scaleX: 0,
      scaleY: 0,
      stroke: useShapeStore.getState().borderColor,
      strokeWidth,
      fill: getFillStyle(),
      strokeUniform: true,
      strokeLineCap: 'round',
      strokeDashArray: getShapeBorder(strokeWidth),
      perPixelTargetFind: true
    })
    paintBoard.canvas?.add(path)
    this.shapeInstance = path
    this.startX = point.x
    this.startY = point.y
    initCustomObjectAttr(path, ELEMENT_CUSTOM_TYPE.SHAPE_BLOCK)
  }

  addPosition(point: fabric.Point | undefined) {
    if (!point || !this.shapeInstance) {
      return
    }
    const { x: moveToX, y: moveToY } = new fabric.Point(point.x, point.y)
    const width = Math.abs(moveToX - this.startX)
    const height = Math.abs(moveToY - this.startY)
    const left = moveToX > this.startX ? this.startX : this.startX - width
    const top = moveToY > this.startY ? this.startY : this.startY - height

    this.shapeInstance.set({
      scaleX: width / 853,
      scaleY: height / 853,
      left,
      top
    })

    this.shapeInstance.setCoords()
    paintBoard.canvas?.requestRenderAll()
  }

  destroy() {
    if (this.shapeInstance) {
      paintBoard.canvas?.remove(this.shapeInstance)
    }
  }
}
