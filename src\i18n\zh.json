{"confirm": "确认", "cancel": "取消", "download": "下载", "reset": "重置", "tool": {"draw": "绘画", "eraser": "橡皮擦", "select": "选择", "board": "画板"}, "title": {"drawType": "绘画类型", "drawStyle": "绘画风格", "drawWidth": "画笔宽度", "drawColor": "画笔颜色", "lineType": "画笔类型", "shadow": "阴影", "AI": "AI", "shapeType": "形状类型", "shapeCount": "形状数量", "materialType": "素材类型", "multiColorType": "多色类型", "drawText": "文字内容", "fontFamily": "字体", "shapeLinePointCount": "线段端点", "borderType": "边框类型", "borderStyle": "边框样式", "fillStyle": "填充样式", "eraserWidth": "橡皮擦宽度", "opacity": "透明度", "layer": "图层", "imageFilters": "图像滤镜", "fontStyle": "字体样式", "canvasBackground": "画板背景", "canvasSize": "画板尺寸", "drawCache": "绘制缓存", "guideLine": "辅助线"}, "canvasSize": {"width": "宽度", "height": "高度"}, "drawType": {"freeStyle": "自由绘画", "shape": "形状绘画"}, "style": {"basic": "基础", "rainbow": "彩虹 ", "shape": "多形状", "material": "素材", "pixels": "像素", "multiColor": "多色", "text": "文字", "multiLine": "多线连接", "reticulate": "网状", "multiPoint": "多点连接", "wiggle": "波浪曲线", "thorn": "荆棘"}, "operate": {"undo": "撤销", "redo": "重做", "copy": "复制", "delete": "删除", "text": "添加文字", "image": "上传图片", "clean": "清除画板", "save": "保存为图片", "fileList": "文件列表", "fullscreen": "进入全屏", "exitFullscreen": "退出全屏"}, "info": {"welecome": "欢迎Star", "FreeStyle": {"line1": "提供了 12 种不同风格的画笔，包括基本画笔，彩虹画笔，多形状画笔，多素材画笔，像素画笔，多色画笔，文字画笔，多线连接画笔，网状画笔，多点连接画笔，波浪曲线画笔，荆棘画笔。满足多样化的绘画", "line2": "所有画笔均支持颜色和画笔宽度的配置", "line3": "多形状可以灵活配置形状类型和数量", "line4": "素材和多色可以调整画笔的素材类型", "line5": "文字绘制支持循环文案配置和字体修改"}, "ShapeDraw": {"line1": "提供了多种常见形状的绘制, 并支持多端点线段以及箭头. 并且这些形状均支持边框和填充的样式配置"}, "EraserMode": {"line1": "橡皮擦模式可以线性擦除所有内容", "line2": "支持线性宽度配置"}, "SelectMode": {"line1": "在选择模式下，可以通过点击绘画内容进行框选", "line2": "点击手柄支持拖拽、缩放和旋转操作，提供灵活的编辑方式", "line3": "选择图片支持多种滤镜配置", "line4": "选择文字时，支持字体设置", "line5": "所有绘制内容均支持图层设置，包括向上移动层级、向下移动层级、移动至顶层和移动至底层", "line6": "所有绘制内容支持透明度配置"}, "BoardMode": {"line1": "画板支持配置背景配置, 包括颜色, 背景图, 透明度", "line2": "画板支持自定义宽高配置", "line3": "支持绘制缓存开启. 在存在大量绘制内容的情况下，启用缓存将提高绘制性能，而禁用缓存则会提升画布清晰度", "line4": "支持辅助线的开启与关闭"}, "BorderConfig": {"line1": "左下角按钮实时显示当前缩放比例，点击即可重置缩放比例", "line2": "中间按钮列表按从左到右的顺序分别为：撤销、反撤销、复制当前选择内容、删除当前选择内容、绘制文字、上传图片、清除绘制内容、保存为图片、打开文件列表", "line3": "电脑端：按住 Space 键并点击鼠标左键可移动画布，滚动鼠标滚轮实现画布缩放，按住 Backspace 键可删除已选内容，同时按住 Ctrl 键 + V 键可粘贴剪贴板图片", "line4": "移动端：支持双指按压后拖拽和缩放画布"}, "FileConfig": {"line1": "多文件配置：支持多个画布切换，每个画布可自定义标题、增加、删除，并提供上传和下载功能"}}, "cleanModal": {"title": "确认清除内容？"}, "deleteFileModal": {"title": "确认删除当前文件吗？"}, "toast": {"uploadFileFail": "上传失败，请重试"}, "filters": {"Sepia": "复古", "Invert": "底片", "BlackWhite": "黑白", "Grayscale": "灰度", "Blur": "模糊", "Vintage": "古典", "BlendColor": "混色", "Brownie": "棕仙", "Kodachrome": "胶片", "Pixelate": "像素", "Polaroid": "宝丽来", "Technicolor": "彩色", "Brightness": "增亮", "Noise": "噪点", "Convolute": "浮雕"}, "fontStyle": {"bold": "粗体", "italic": "斜体", "underLine": "下划线", "lineThrough": "删除线"}, "boardConfig": {"cacheTip": "在存在大量绘制内容的情况下，启用缓存将提高绘制性能，而禁用缓存则会提升画布清晰度"}, "request": {"tip": "请自由绘画...", "loading": "正在加载数据，请稍候...", "error": "出错了。请稍后再试。"}, "uploadImage": {"removeBackground": "去除背景", "removeBackgroundGpuTip": "本浏览器不支持WebGPU, 要使用去除背景功能请使用最新版谷歌浏览器", "removeBackgroundLoading": "去除背景功能加载中", "removeBackgroundFailed": "去除背景功能加载失败", "removeBackgroundSuccess": "去除背景功能加载成功", "removeBackgroundProcessing": "去除背景处理中", "removeBackgroundProcessingSuccess": "去除背景处理成功", "restore": "还原", "upload": "上传", "imageSegmentation": "图像分割", "clearPoints": "清除标记点", "imageSegmentationLoading": "图像分割功能加载中", "imageSegmentationFailed": "图像分割功能加载失败", "imageSegmentationSuccess": "图像分割功能加载成功", "imageSegmentationProcessing": "处理图像中", "imageSegmentationProcessed": "图像处理成功, 可点击图像进行标记, 绿色蒙层区域就是分割区域", "imageSegmentationGpuTip": "本浏览器不支持WebGPU, 要使用图像分割功能请使用最新版谷歌浏览器", "positivePoint": "正标记", "negativePoint": "负标记"}, "downloadImage": {"rotate": "旋转", "scale": "缩放", "size": "尺寸"}, "eraserConfig": {"eraser": "橡皮擦", "erasable": "可擦除"}}