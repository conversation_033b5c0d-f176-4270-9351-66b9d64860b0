{"name": "paint-board", "private": true, "version": "1.5.7", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "pnpm lint:script && pnpm lint:style", "lint:script": "eslint --ext .ts,.tsx --fix --quiet ./", "lint:style": "stylelint --fix \"src/**/*.css\""}, "dependencies": {"@huggingface/transformers": "^3.0.0", "daisyui": "^2.46.1", "fabric": "^5.3.0", "i18next": "^22.4.9", "idb-keyval": "^6.2.1", "immer": "^10.0.3", "jsondiffpatch": "^0.6.0", "lodash": "^4.17.21", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^12.1.5", "react-image-crop": "^11.0.7", "swiper": "^11.0.5", "uuid": "^9.0.1", "zustand": "^4.4.7"}, "devDependencies": {"@commitlint/cli": "^17.4.1", "@commitlint/config-conventional": "^17.4.0", "@stylelint/postcss-css-in-js": "^0.38.0", "@types/fabric": "^5.3.6", "@types/lodash": "^4.14.191", "@types/node": "^18.11.18", "@types/react": "^18.0.26", "@types/react-dom": "^18.0.10", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^5.48.1", "@typescript-eslint/parser": "^5.48.1", "@vitejs/plugin-react": "^2.2.0", "autoprefixer": "^10.4.13", "eslint": "^8.31.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.31.11", "husky": "^8.0.3", "lint-staged": "^13.1.0", "postcss": "^8.4.21", "postcss-syntax": "^0.36.2", "prettier": "^2.8.2", "stylelint": "^14.16.1", "stylelint-config-prettier": "^9.0.4", "stylelint-config-recess-order": "^3.1.0", "stylelint-config-standard": "^29.0.0", "stylelint-prettier": "^2.0.0", "tailwindcss": "^3.2.4", "typescript": "^4.9.4", "vite": "^3.2.5", "vite-plugin-eslint": "^1.8.1", "vite-plugin-pwa": "0.16.7", "vite-plugin-svgr": "^4.2.0"}, "lint-staged": {"*.{js,jsx,tsx,ts}": ["pnpm lint:script"], "*.css": ["pnpm lint:style"]}}