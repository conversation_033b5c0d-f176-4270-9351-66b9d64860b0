import { fabric } from 'fabric'
import { paintBoard } from '@/core/paintBoard'
import { initCustomObjectAttr } from '@/core/utils/object'
import { getFillStyle, getShapeBorder, getShapeBorderWidth } from './utils'
import useShapeStore from '@/store/shape'
import { ELEMENT_CUSTOM_TYPE } from '@/constants'

export class SpeakerShape {
  shapeInstance: fabric.Object | undefined
  startX = 0
  startY = 0

  constructor(point: fabric.Point | undefined) {
    if (!point) {
      return
    }

    const strokeWidth = getShapeBorderWidth()

    const pathStr = `m489.01486,786.432c29.14743,0 50.13943,-21.43086 50.13943,-50.13943l0,-683.57486c0,-28.70857 -20.992,-52.71771 -50.99886,-52.71771c-20.992,0 -34.70628,10.29486 -57.856,30.86629l-201.43543,178.70628c-2.56,2.56 -6.41828,3.85829 -10.69714,3.85829l-127.28686,0c-60.87314,0 -90.88,30.42743 -90.88,95.14057l0,170.58743c0,64.73143 30.00686,95.14057 90.88,95.14057l127.26858,0c4.29714,0 8.15542,1.28 10.71542,3.85828l201.45372,180.40686c20.992,18.87086 37.72343,27.86743 58.71543,27.86743l-0.01829,0z`

    const path = new fabric.Path(pathStr, {
      left: point.x,
      top: point.y,
      scaleX: 0,
      scaleY: 0,
      stroke: useShapeStore.getState().borderColor,
      strokeWidth,
      fill: getFillStyle(),
      strokeUniform: true,
      strokeLineCap: 'round',
      strokeDashArray: getShapeBorder(strokeWidth),
      perPixelTargetFind: true
    })
    paintBoard.canvas?.add(path)
    this.shapeInstance = path
    this.startX = point.x
    this.startY = point.y
    initCustomObjectAttr(path, ELEMENT_CUSTOM_TYPE.SHAPE_SPEAKER)
  }

  addPosition(point: fabric.Point | undefined) {
    if (!point || !this.shapeInstance) {
      return
    }
    const { x: moveToX, y: moveToY } = new fabric.Point(point.x, point.y)
    const width = Math.abs(moveToX - this.startX)
    const height = Math.abs(moveToY - this.startY)
    const left = moveToX > this.startX ? this.startX : this.startX - width
    const top = moveToY > this.startY ? this.startY : this.startY - height

    this.shapeInstance.set({
      scaleX: width / 539,
      scaleY: height / 786,
      left,
      top
    })

    this.shapeInstance.setCoords()
    paintBoard.canvas?.requestRenderAll()
  }

  destroy() {
    if (this.shapeInstance) {
      paintBoard.canvas?.remove(this.shapeInstance)
    }
  }
}
